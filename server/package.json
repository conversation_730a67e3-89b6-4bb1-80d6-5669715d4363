{"name": "travel-mate-backend", "version": "1.0.0", "description": "Backend API for Travel Mate - South African taxi tracking application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:unit": "jest --testPathPattern=tests/unit", "test:integration": "jest --testPathPattern=tests/integration", "test:e2e": "jest --testPathPattern=tests/e2e", "test:coverage": "jest --coverage", "db:create": "node scripts/createDatabase.js", "db:migrate": "node scripts/migrate.js", "db:rollback": "node scripts/rollback.js", "db:seed": "node scripts/seed.js", "db:reset": "npm run db:create && npm run db:migrate && npm run db:seed", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": ["taxi", "tracking", "south-africa", "community", "transport", "api"], "author": "Travel Mate Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "express-validator": "^6.15.0", "helmet": "^6.1.5", "jsonwebtoken": "^9.0.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "pg": "^8.11.0", "pg-pool": "^3.6.0"}, "devDependencies": {"eslint": "^8.40.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.5.0", "nodemon": "^3.1.10", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}